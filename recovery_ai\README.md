# Recovery AI - Mobile App

A minimal Flutter mobile app designed to serve as a placeholder for reserving the "Recovery AI" app name on the Apple App Store and Google Play Store. This app aligns with the recovery empire's AA/NA-inspired mission and promotes related brands.

## Features

- **Splash Screen**: Displays the Recovery AI logo and branding for 3 seconds
- **Quote Screen**: Shows an inspirational recovery quote ("Serenity in every step")
- **Coming Soon Screen**: Informs users about upcoming features and links to Recovery Merch

## Tech Stack

- **Framework**: Flutter 3.29.2
- **Language**: Dart 3.7.2
- **Dependencies**:
  - `url_launcher` for external link handling
  - Material Design components

## Project Structure

```
lib/
├── main.dart                 # App entry point and theme configuration
└── screens/
    ├── splash_screen.dart    # Initial loading screen with 3-second timer
    ├── quote_screen.dart     # Recovery quote display
    └── coming_soon_screen.dart # Future features and external link
```

## Getting Started

### Prerequisites

- Flutter SDK 3.22+ installed
- Dart SDK 3.7+
- Android Studio or VS Code with Flutter extensions
- Chrome browser (for web testing)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd recovery_ai
```

2. Install dependencies:
```bash
flutter pub get
```

3. Run the app:
```bash
# For web (Chrome)
flutter run -d chrome

# For Android emulator
flutter run -d android

# For iOS simulator (macOS only)
flutter run -d ios
```

## Development

### Running Tests

```bash
flutter test
```

### Code Analysis

```bash
flutter analyze
```

### Building for Release

#### Android
```bash
flutter build appbundle
# or
flutter build apk
```

#### iOS
```bash
flutter build ios
```

## App Store Submission

This app is designed to meet basic app store requirements:

- **Apple App Store**: Complies with minimum functionality guidelines
- **Google Play Store**: Meets spam and minimum functionality policies
- **Privacy**: No user data collection, privacy policy hosted at recoveryai.org
- **Content**: Recovery-focused, appropriate for all ages

## Brand Integration

The app promotes the recovery empire's brands:

- **Recovery Merch**: Direct link to recoverymerch.org
- **Recovery-themed design**: Blue color scheme, calming typography
- **Future integration**: Planned connection to Golang management system

## Future Features (Coming Soon)

- Sobriety tracker
- Daily quotes rotation
- Recovery videos integration
- Community support features
- Push notifications
- Integration with existing Golang backend

## Contributing

This is a placeholder app with minimal functionality. For feature requests or issues, please contact the development team.

## License

Private project for Recovery Empire brands.
