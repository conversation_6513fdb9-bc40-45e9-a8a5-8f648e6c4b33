# Design Document: Recovery AI Placeholder Mobile App

## Overview
**Purpose**: A minimal mobile app built in Flutter named "Recovery AI" to serve as a placeholder for reserving the app name on the Apple App Store (iOS) and Google Play Store (Android). The app will meet basic store submission requirements while aligning with the recovery empire's AA/NA-inspired mission. It acts as a stepping stone to the full app, providing a simple entry point that promotes the empire's brands, including Recovery Merch (recoverymerch.org), Sound of Recovery (YouTube), and Recovery Box/Monthly (subscription box).

**Scope**: 
- Basic features only: Splash screen, static recovery quote page, and "Coming Soon" page with a link to recoverymerch.org.
- No data collection, backend integration, or advanced functionality in this version to minimize development time and ensure quick approval.
- Cross-platform for iOS and Android.
- Scalable for future expansion (e.g., sobriety tracker, daily quotes, YouTube QR links).

**Assumptions**:
- App name "Recovery AI" is unique and available on stores (verified via developer consoles).
- Developer accounts are set up: Apple ($99/year), Google ($25 one-time).
- No user data collected, complying with privacy policies (hosted on recoveryai.org).
- Development environment: Flutter SDK, Dart, VS Code or Android Studio.

## Tech Stack
- **Framework**: Flutter (v3.22 or higher) for cross-platform development (single codebase for iOS/Android, chosen for speed and consistent UI).
- **Language**: Dart (Flutter's language).
- **Tools/Libraries**:
  - Flutter's built-in widgets for UI (e.g., `MaterialApp` for splash/home screens).
  - `url_launcher` package for linking to recoverymerch.org (simple URL opening).
  - Canva (free tier) for designing app icon, splash screen background, and store screenshots.
- **Deployment**: Build for iOS (`.ipa` via Xcode) and Android (`.apk` or `.aab` via Android Studio). Submit to App Store Connect and Google Play Console.
- **No Backend**: Static content only; future versions will integrate with the Golang management system's REST APIs (e.g., for quotes from `/schedules` endpoint).

## Architecture
- **App Structure**: Single-screen flow with minimal navigation.
  - **Entry Point**: `main.dart` initializes the app with a `MaterialApp`.
  - **Screens**:
    - Splash Screen: Displays logo/background for 2–3 seconds (using `Future.delayed`).
    - Quote Page: Static text widget with a recovery quote (e.g., “One day at a time”).
    - Coming Soon Page: Text widget with description and button to open recoverymerch.org (using `url_launcher`).
  - **Navigation**: Basic `Navigator` or `MaterialPageRoute` to move between screens.
- **UI Design**: Clean, calming theme (blue tones, sans-serif fonts) to reflect recovery focus. Use Flutter's `ThemeData` for consistency.
- **Modularity**: Separate Dart files for each screen (e.g., `splash_screen.dart`, `quote_screen.dart`).
- **Security/Privacy**: No permissions requested (e.g., no internet access needed beyond URL launch). Privacy policy hosted on recoveryai.org.
- **Data Flow**: Fully static—no API calls or storage. Future integration: REST calls to Golang system for dynamic content.

## Modules and Development Steps
Each module includes specific steps for delegation, with deliverables, estimated time (for 1 developer), and dependencies. Total estimated time: ~20 hours.

### Module 1: Project Setup
**Goal**: Initialize Flutter project and configure for iOS/Android.

**Steps**:
1. Install Flutter SDK and Dart (flutter.dev/setup).
2. Create project: `flutter create recovery_ai --org org.recoverymerch --project-name recovery_ai`.
3. Set package name (Android: `com.recoverymerch.recoveryai`) and bundle ID (iOS: `org.recoverymerch.recoveryai`) in `pubspec.yaml`.
4. Add `url_launcher` package: `flutter pub add url_launcher`.
5. Test initial build: `flutter run` on emulator.

**Deliverable**: Running Flutter project with default template.
**Est. Time**: 3 hours.
**Dependencies**: Flutter SDK, Android Studio/Xcode installed.

### Module 2: UI Development
**Goal**: Build the three core screens with recovery-themed design.

**Steps**:
1. Design assets in Canva: App icon (1024x1024), splash background (e.g., blue gradient with "Recovery AI" text).
2. Implement Splash Screen: Use `Future.delayed` to show logo for 3 seconds, then navigate to Quote Page.
3. Implement Quote Page: `Scaffold` with centered `Text` widget for quote (hardcoded: “Serenity in every step”).
4. Implement Coming Soon Page: `Scaffold` with `Text` ("Full app coming soon!") and `ElevatedButton` to launch recoverymerch.org URL.
5. Add navigation: Use `Navigator.push` between pages.
6. Apply theme: Set `ThemeData` in `MaterialApp` (primary color: blue, font: Google Fonts sans-serif).

**Deliverable**: App with 3 screens, tested on iOS/Android emulators.
**Est. Time**: 8 hours.
**Dependencies**: Project setup, Canva assets.

### Module 3: Testing and Polish
**Goal**: Ensure app meets store standards without crashes.

**Steps**:
1. Test on emulators: iOS (Xcode) and Android (Android Studio) for navigation, URL launch, and UI consistency.
2. Add error handling: Basic try-catch for URL launch.
3. Optimize: Ensure app size < 50MB; remove unnecessary dependencies.
4. Prepare store assets: 5 iOS screenshots (1242x2688), 2 Android screenshots (1080x1920), feature graphic (1024x500) in Canva.

**Deliverable**: Tested app ready for build/export.
**Est. Time**: 4 hours.
**Dependencies**: UI development.

### Module 4: Build and Submission
**Goal**: Export builds and submit to stores.

**Steps**:
1. Build for iOS: `flutter build ios` (requires Xcode).
2. Build for Android: `flutter build appbundle` (preferred for Google) or `flutter build apk`.
3. Submit to App Store Connect: Upload binary, add metadata (description: “Recovery AI: Sobriety support app, coming soon. Visit recoverymerch.org”), privacy policy URL (recoveryai.org/privacy-policy).
4. Submit to Google Play Console: Upload AAB/APK, add metadata, Data Safety form (no data collected).
5. Monitor reviews (1–7 days).

**Deliverable**: App submitted, name reserved.
**Est. Time**: 5 hours.
**Dependencies**: Testing, developer accounts.

## Development Plan
- **Total Est. Time**: ~20 hours (Setup: 3h, UI: 8h, Testing: 4h, Build/Submission: 5h).
- **Delegation**: Assign to a Flutter developer (e.g., Upwork at $30–$50/hour).
- **Testing**: Run on physical devices if possible; focus on no crashes (Apple Guideline 2.1).
- **Documentation**: Add README.md with setup instructions; include in Golang Task Management for tracking.

## Integration with Golang System
- **Task Management**: Add app tasks (e.g., “Build Flutter splash screen”) to the `tasks` table.
- **Content Scheduling**: Schedule submission updates (e.g., “Post Recovery AI submission status to TikTok”).
- **Analytics**: Track app store impressions post-submission.

This design doc is ready for implementation. If you need code snippets (e.g., main.dart) or revisions, let me know! Want to add this to your Trello board or focus on something else, like Recovery Box/Monthly domain checks?